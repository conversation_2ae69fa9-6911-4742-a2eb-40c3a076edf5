import React from 'react';
import { render, fireEvent, cleanup } from '@testing-library/react-native';
import { Context as ResponsiveContext } from 'react-responsive';

import { ANALYTICS_ACTION_TYPE, EVENT_TYPE } from 'src/constants.events';
import { AdvertiseInADirectory } from './AdvertiseInADirectory';

const mockLogEvents = jest.fn();

jest.mock('src/services/analytics', () => {
  return {
    logEvent: (...args: unknown[]) => mockLogEvents(...args),
  };
});

describe('Screens | Advertise in a directory', () => {
  afterEach(cleanup);

  it('renders the screen', () => {
    const { getByTestId } = render(<AdvertiseInADirectory />);

    const advertiseInADirectoryScreen = getByTestId(
      AdvertiseInADirectory.testIds.ROOT,
    );
    expect(advertiseInADirectoryScreen).toBeVisible();
  });

  it('should log "advertise_directory_viewed" event when the screen is viewed', () => {
    render(<AdvertiseInADirectory />);

    expect(mockLogEvents).toHaveBeenCalledWith(
      `advertise_directory_${ANALYTICS_ACTION_TYPE.VIEWED}`,
    );
  });

  it('should display Call Us button on mobile', async () => {
    const { queryByTestId, queryAllByTestId } = render(
      <ResponsiveContext.Provider value={{ width: 320 }}>
        <AdvertiseInADirectory />
      </ResponsiveContext.Provider>,
    );

    const descriptionCallUsButton = queryByTestId(
      AdvertiseInADirectory.testIds.CALL_US_BUTTON,
    );
    const mobileCallUsButton = queryByTestId(
      `${AdvertiseInADirectory.testIds.CALL_US_BUTTON}_mobile`,
    );
    const callUsText = queryAllByTestId(
      AdvertiseInADirectory.testIds.CALL_US_TEXT,
    );

    expect(descriptionCallUsButton).toBeVisible();
    expect(mobileCallUsButton).toBeVisible();
    expect(callUsText).toHaveLength(0);
  });

  it('should log "call_button" event when the Call Us button is pressed', async () => {
    const { getByTestId } = render(
      <ResponsiveContext.Provider value={{ width: 320 }}>
        <AdvertiseInADirectory />
      </ResponsiveContext.Provider>,
    );

    const callUsButton = getByTestId(
      AdvertiseInADirectory.testIds.CALL_US_BUTTON,
    );

    fireEvent.press(callUsButton);
    expect(mockLogEvents).toHaveBeenCalledWith(EVENT_TYPE.DIRECTORIES_CALL_US);
  });

  it('should log "request_callback" event when the Book A Call Back button is pressed', async () => {
    const { getByTestId } = render(<AdvertiseInADirectory />);

    const callbackButton = getByTestId(
      AdvertiseInADirectory.testIds.CALLBACK_REQUEST_BUTTON,
    );

    fireEvent.press(callbackButton);

    expect(mockLogEvents).toHaveBeenCalledWith(
      EVENT_TYPE.DIRECTORIES_REQUEST_CALLBACK,
    );
  });

  it('should display carousel on mobile', () => {
    const { getByTestId, queryByTestId } = render(
      <ResponsiveContext.Provider value={{ width: 320 }}>
        <AdvertiseInADirectory />
      </ResponsiveContext.Provider>,
    );

    expect(
      getByTestId(AdvertiseInADirectory.testIds.AD_CAROUSEL),
    ).toBeVisible();
    expect(queryByTestId(AdvertiseInADirectory.testIds.AD_GRID)).toBeNull();
  });

  it('should display grid on desktop', () => {
    const { getByTestId, queryByTestId } = render(
      <ResponsiveContext.Provider value={{ width: 1200 }}>
        <AdvertiseInADirectory />
      </ResponsiveContext.Provider>,
    );

    expect(getByTestId(AdvertiseInADirectory.testIds.AD_GRID)).toBeVisible();
    expect(queryByTestId(AdvertiseInADirectory.testIds.AD_CAROUSEL)).toBeNull();
  });
});
