import React from 'react';
import { View } from 'react-native';

import { Typography } from '@cat-home-experts/react-native-components';
import { createMortarStyles } from '@cat-home-experts/react-native-utilities';
import { AspectRatioImage } from 'src/components/primitives/AspectRatioImage';
import { type AdType } from '../constants';

export interface AdTypeCardProps {
  adType: AdType;
  testID?: string;
}

export const AdTypeCard: React.FC<AdTypeCardProps> = ({ adType, testID }) => {
  return (
    <View testID={testID} style={styles.adCard}>
      <View style={styles.adCardImageContainer}>
        <View style={styles.adCardImagesWrapper}>
          <AspectRatioImage
            style={styles.adCardVisualImage}
            source={adType.visualImage}
            dimensionToCalculate="height"
            aspectRatio={0.6}
            resizeMode="contain"
            accessible
            accessibilityLabel={`${adType.title} visual example`}
            accessibilityRole="image"
          />
          <AspectRatioImage
            style={styles.adCardAdImage}
            source={adType.adImage}
            dimensionToCalculate="height"
            aspectRatio={0.6}
            resizeMode="contain"
            accessible
            accessibilityLabel={`${adType.title} advertisement example`}
            accessibilityRole="image"
          />
        </View>
      </View>
      <View style={styles.adCardContent}>
        <Typography use="subHeader" style={styles.adCardTitle}>
          {adType.title}
        </Typography>
        <Typography use="bodyRegular" style={styles.adCardDescription}>
          {adType.description}
        </Typography>
      </View>
    </View>
  );
};

const styles = createMortarStyles(({ palette, spacing }) => ({
  adCard: {
    backgroundColor: palette.mortar.tokenColorPrimaryWhite,
    borderRadius: 8,
    shadowColor: palette.mortar.tokenColorBlack,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    overflow: 'hidden',
    minWidth: 280,
    flex: 1,
  },
  adCardImageContainer: {
    backgroundColor: '#E6F3FF', // Light blue background
    padding: spacing(2),
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 180,
  },
  adCardImagesWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: spacing(1),
    width: '100%',
  },
  adCardVisualImage: {
    flex: 1,
    maxWidth: 95,
  },
  adCardAdImage: {
    flex: 1,
    maxWidth: 95,
  },
  adCardContent: {
    padding: spacing(2),
    flex: 1,
  },
  adCardTitle: {
    marginBottom: spacing(1),
    fontWeight: '600',
  },
  adCardDescription: {
    fontSize: 14,
    lineHeight: 20,
    color: palette.mortar.tokenColorDarkGrey,
  },
}));
