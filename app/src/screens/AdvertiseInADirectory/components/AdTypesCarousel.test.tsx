import React from 'react';
import { cleanup, render } from '@testing-library/react-native';
import { AdTypesCarousel } from './AdTypesCarousel';
import { AD_TYPES, ADVERTISE_IN_DIRECTORY_TEXT } from '../constants';

jest.mock('src/hooks/useMediaQuery', () => ({
  useDesktopMediaQuery: () => false,
}));

// Mock the Carousel component to render items directly for testing
jest.mock('src/components/Carousel/Carousel', () => {
  // eslint-disable-next-line @typescript-eslint/no-var-requires
  const React = require('react');
  // eslint-disable-next-line @typescript-eslint/no-var-requires
  const ReactNative = require('react-native');
  const { View } = ReactNative;

  return {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    Carousel: ({ data, renderItem, testID }: any) => {
      return React.createElement(
        View,
        { testID },
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        data.map((item: any, index: number) =>
          React.createElement(View, { key: `item-${index}` }, renderItem(item)),
        ),
      );
    },
  };
});

describe('AdTypesCarousel', () => {
  afterEach(() => {
    cleanup();
    jest.clearAllMocks();
  });

  it('renders the section title correctly', () => {
    const { getByText } = render(<AdTypesCarousel />);

    expect(
      getByText(ADVERTISE_IN_DIRECTORY_TEXT.AD_TYPES_TITLE),
    ).toBeOnTheScreen();
  });

  it('renders with custom testID', () => {
    const testID = 'custom-carousel-test-id';
    const { getByTestId } = render(<AdTypesCarousel testID={testID} />);

    expect(getByTestId(testID)).toBeOnTheScreen();
  });

  it('renders all ad type cards', () => {
    const { getByTestId } = render(<AdTypesCarousel />);

    AD_TYPES.forEach((adType) => {
      expect(getByTestId(`ad-type-card-${adType.id}`)).toBeOnTheScreen();
    });
  });

  it('renders ad type titles and descriptions', () => {
    const { getByText } = render(<AdTypesCarousel />);

    AD_TYPES.forEach((adType) => {
      expect(getByText(adType.title)).toBeOnTheScreen();
      expect(getByText(adType.description)).toBeOnTheScreen();
    });
  });

  it('renders without testID when not provided', () => {
    const { queryByTestId } = render(<AdTypesCarousel />);

    // Should not crash when testID is not provided
    expect(queryByTestId('custom-carousel-test-id')).toBeNull();
  });

  it('passes correct props to Carousel component', () => {
    const testID = 'test-carousel';
    render(<AdTypesCarousel testID={testID} />);

    // The carousel should render with the provided testID
    // This is tested indirectly through the mock implementation
    expect(true).toBe(true); // Placeholder assertion since we're testing through mocks
  });

  it('renders the correct number of ad types', () => {
    const { getAllByText } = render(<AdTypesCarousel />);

    // Check that we have the expected number of ad type titles
    const expectedCount = AD_TYPES.length;
    let actualCount = 0;

    AD_TYPES.forEach((adType) => {
      const elements = getAllByText(adType.title);
      actualCount += elements.length;
    });

    expect(actualCount).toBe(expectedCount);
  });
});
