import React from 'react';
import { View } from 'react-native';

import { Typography } from '@cat-home-experts/react-native-components';
import { createMortarStyles } from '@cat-home-experts/react-native-utilities';
import { Carousel } from 'src/components/Carousel/Carousel';
import {
  AD_TYPES,
  ADVERTISE_IN_DIRECTORY_TEXT,
  type AdType,
} from '../constants';
import { AdTypeCard } from './AdTypeCard';

export interface AdTypesCarouselProps {
  testID?: string;
}

export const AdTypesCarousel: React.FC<AdTypesCarouselProps> = ({ testID }) => {
  const renderAdType = (item: AdType) => (
    <AdTypeCard adType={item} testID={`ad-type-card-${item.id}`} />
  );

  return (
    <View style={styles.carouselContainer}>
      <Typography use="subHeader" style={styles.sectionTitle}>
        {ADVERTISE_IN_DIRECTORY_TEXT.AD_TYPES_TITLE}
      </Typography>
      <Carousel
        testID={testID}
        data={AD_TYPES}
        renderItem={renderAdType}
        keyExtractor={(item) => item.id}
        containerStyle={styles.carouselWrapper}
        cardHeight={320}
        maxWidth={300}
      />
    </View>
  );
};

const styles = createMortarStyles(({ palette, spacing }) => ({
  carouselContainer: {
    padding: spacing(3),
    backgroundColor: palette.mortar.tokenColorPrimaryWhite,
  },
  carouselWrapper: {
    marginTop: spacing(2),
  },
  sectionTitle: {
    marginBottom: spacing(2),
    fontWeight: '600',
  },
}));
