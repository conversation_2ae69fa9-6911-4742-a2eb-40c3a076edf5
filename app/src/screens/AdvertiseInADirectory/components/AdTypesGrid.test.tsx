import React from 'react';
import { cleanup, render } from '@testing-library/react-native';
import { AdTypesGrid } from './AdTypesGrid';
import { AD_TYPES, ADVERTISE_IN_DIRECTORY_TEXT } from '../constants';

const isMobile = false;

describe('AdTypesGrid', () => {
  afterEach(() => {
    cleanup();
    jest.clearAllMocks();
  });

  it('renders the section title correctly', () => {
    const { getByText } = render(<AdTypesGrid isMobile={isMobile} />);

    expect(
      getByText(ADVERTISE_IN_DIRECTORY_TEXT.AD_TYPES_TITLE),
    ).toBeOnTheScreen();
  });

  it('renders with custom testID', () => {
    const testID = 'custom-grid-test-id';
    const { getByTestId } = render(
      <AdTypesGrid testID={testID} isMobile={isMobile} />,
    );

    expect(getByTestId(testID)).toBeOnTheScreen();
  });

  it('renders all ad type cards', () => {
    const { getByTestId } = render(<AdTypesGrid isMobile={isMobile} />);

    AD_TYPES.forEach((adType) => {
      expect(getByTestId(`ad-type-card-${adType.id}`)).toBeOnTheScreen();
    });
  });

  it('renders ad type titles and descriptions', () => {
    const { getByText } = render(<AdTypesGrid isMobile={isMobile} />);

    AD_TYPES.forEach((adType) => {
      expect(getByText(adType.title)).toBeOnTheScreen();
      expect(getByText(adType.description)).toBeOnTheScreen();
    });
  });

  it('renders without testID when not provided', () => {
    const { queryByTestId } = render(<AdTypesGrid isMobile={isMobile} />);

    // Should not crash when testID is not provided
    expect(queryByTestId('custom-grid-test-id')).toBeNull();
  });

  it('renders the correct number of ad types', () => {
    const { getAllByText } = render(<AdTypesGrid isMobile={isMobile} />);

    // Check that we have the expected number of ad type titles
    const expectedCount = AD_TYPES.length;
    let actualCount = 0;

    AD_TYPES.forEach((adType) => {
      const elements = getAllByText(adType.title);
      actualCount += elements.length;
    });

    expect(actualCount).toBe(expectedCount);
  });

  it('renders FlatList with correct configuration', () => {
    const { getByTestId } = render(
      <AdTypesGrid testID="test-grid" isMobile={isMobile} />,
    );

    const flatList = getByTestId('test-grid');
    expect(flatList).toBeOnTheScreen();
  });

  it('renders all ad types in grid format', () => {
    const { getByTestId } = render(<AdTypesGrid isMobile={isMobile} />);

    // Verify that all expected ad types are rendered
    const expectedAdTypes = [
      'full-page-ad',
      'back-half-page-ad',
      'premium-ad',
      'standard-ad',
    ];

    expectedAdTypes.forEach((adTypeId) => {
      expect(getByTestId(`ad-type-card-${adTypeId}`)).toBeOnTheScreen();
    });
  });

  it('handles empty data gracefully', () => {
    // This test ensures the component doesn't crash with empty data
    // Since we're using the actual AD_TYPES constant, we test the normal case
    const { getByText } = render(<AdTypesGrid isMobile={isMobile} />);

    expect(
      getByText(ADVERTISE_IN_DIRECTORY_TEXT.AD_TYPES_TITLE),
    ).toBeOnTheScreen();
  });
});
