import React from 'react';
import { View, FlatList } from 'react-native';

import { Typography } from '@cat-home-experts/react-native-components';
import { createMortarStyles } from '@cat-home-experts/react-native-utilities';
import {
  AD_TYPES,
  ADVERTISE_IN_DIRECTORY_TEXT,
  type AdType,
} from '../constants';
import { AdTypeCard } from './AdTypeCard';

export interface AdTypesGridProps {
  testID?: string;
  isMobile: boolean;
}

export const AdTypesGrid: React.FC<AdTypesGridProps> = ({
  testID,
  isMobile,
}) => {
  const renderAdType = ({ item }: { item: AdType }) => (
    <View style={styles.gridItem}>
      <AdTypeCard adType={item} testID={`ad-type-card-${item.id}`} />
    </View>
  );

  return (
    <View
      style={[
        styles.gridContainer,
        !isMobile && styles.borderRadiusTop,
        !isMobile && styles.marginTop,
      ]}
    >
      <Typography use="subHeader" style={styles.sectionTitle}>
        {ADVERTISE_IN_DIRECTORY_TEXT.AD_TYPES_TITLE}
      </Typography>
      <FlatList
        testID={testID}
        data={AD_TYPES}
        renderItem={renderAdType}
        keyExtractor={(item) => item.id}
        numColumns={2}
        contentContainerStyle={styles.gridContent}
        columnWrapperStyle={styles.gridRow}
      />
    </View>
  );
};

const styles = createMortarStyles(({ palette, spacing }) => ({
  gridContainer: {
    padding: spacing(3),
    backgroundColor: palette.mortar.tokenColorPrimaryWhite,
  },
  gridContent: {
    gap: spacing(2),
  },
  gridRow: {
    justifyContent: 'space-between',
    gap: spacing(2),
  },
  gridItem: {
    flex: 1,
    maxWidth: '48%',
  },
  sectionTitle: {
    marginBottom: spacing(2),
    fontWeight: '600',
  },
  borderRadiusTop: {
    borderTopRightRadius: spacing(1),
    borderTopLeftRadius: spacing(1),
  },
  marginTop: {
    marginTop: spacing(2),
  },
}));
