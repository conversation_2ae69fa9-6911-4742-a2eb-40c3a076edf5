import React from 'react';
import { cleanup, fireEvent, render } from '@testing-library/react-native';
import { DescriptionBlock } from './DescriptionBlock';
import { ADVERTISE_IN_DIRECTORY_TEXT } from '../constants';

const mockOnCallUs = jest.fn();
const mockOnRequestCallback = jest.fn();

describe('DescriptionBlock', () => {
  afterEach(() => {
    cleanup();
    jest.clearAllMocks();
  });

  const defaultProps = {
    isMobile: true,
    onCallUs: mockOnCallUs,
    onRequestCallback: mockOnRequestCallback,
    callUsButtonTestId: 'test-call-us-button',
    callbackRequestButtonTestId: 'test-callback-button',
  };

  it('renders all text content correctly', () => {
    const { getByText } = render(<DescriptionBlock {...defaultProps} />);

    expect(getByText(ADVERTISE_IN_DIRECTORY_TEXT.MAIN_TITLE)).toBeOnTheScreen();
    expect(
      getByText(ADVERTISE_IN_DIRECTORY_TEXT.DESCRIPTION),
    ).toBeOnTheScreen();
    expect(
      getByText(ADVERTISE_IN_DIRECTORY_TEXT.CALL_US_BUTTON),
    ).toBeOnTheScreen();
    expect(
      getByText(ADVERTISE_IN_DIRECTORY_TEXT.BOOK_CALLBACK_BUTTON),
    ).toBeOnTheScreen();
  });

  it('renders buttons with correct test IDs', () => {
    const { getByTestId } = render(<DescriptionBlock {...defaultProps} />);

    expect(getByTestId('test-call-us-button')).toBeOnTheScreen();
    expect(getByTestId('test-callback-button')).toBeOnTheScreen();
  });

  it('calls onCallUs when call us button is pressed', () => {
    const { getByTestId } = render(<DescriptionBlock {...defaultProps} />);

    const callUsButton = getByTestId('test-call-us-button');
    fireEvent.press(callUsButton);

    expect(mockOnCallUs).toHaveBeenCalledTimes(1);
  });

  it('calls onRequestCallback when callback button is pressed', () => {
    const { getByTestId } = render(<DescriptionBlock {...defaultProps} />);

    const callbackButton = getByTestId('test-callback-button');
    fireEvent.press(callbackButton);

    expect(mockOnRequestCallback).toHaveBeenCalledTimes(1);
  });

  it('renders without test IDs when not provided', () => {
    const { queryByTestId } = render(
      <DescriptionBlock
        isMobile={true}
        onCallUs={mockOnCallUs}
        onRequestCallback={mockOnRequestCallback}
      />,
    );

    expect(queryByTestId('test-call-us-button')).toBeNull();
    expect(queryByTestId('test-callback-button')).toBeNull();
  });

  it('renders correctly on desktop', () => {
    const { getByText } = render(
      <DescriptionBlock {...defaultProps} isMobile={false} />,
    );

    expect(getByText(ADVERTISE_IN_DIRECTORY_TEXT.MAIN_TITLE)).toBeOnTheScreen();
    expect(
      getByText(ADVERTISE_IN_DIRECTORY_TEXT.DESCRIPTION),
    ).toBeOnTheScreen();
  });

  it('has correct button variants', () => {
    const { getByText } = render(<DescriptionBlock {...defaultProps} />);

    const callUsButton = getByText(ADVERTISE_IN_DIRECTORY_TEXT.CALL_US_BUTTON);
    const callbackButton = getByText(
      ADVERTISE_IN_DIRECTORY_TEXT.BOOK_CALLBACK_BUTTON,
    );

    expect(callUsButton).toBeOnTheScreen();
    expect(callbackButton).toBeOnTheScreen();
  });
});
