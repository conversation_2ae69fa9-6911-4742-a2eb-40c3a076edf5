import React from 'react';
import { cleanup, render } from '@testing-library/react-native';
import { HeroSection } from './HeroSection';
import { ADVERTISE_IN_DIRECTORY_TEXT } from '../constants';

describe('HeroSection', () => {
  afterEach(() => {
    cleanup();
    jest.clearAllMocks();
  });

  const defaultProps = {
    isMobile: true,
    heroImageTestId: 'test-hero-image',
  };

  it('renders correctly on mobile', () => {
    const { getByTestId, getByLabelText } = render(
      <HeroSection {...defaultProps} />,
    );

    const heroImage = getByTestId('test-hero-image');
    expect(heroImage).toBeOnTheScreen();

    const accessibleImage = getByLabelText(
      ADVERTISE_IN_DIRECTORY_TEXT.DIRECTORY_IMAGE_ALT,
    );
    expect(accessibleImage).toBeOnTheScreen();
  });

  it('renders correctly on desktop', () => {
    const { getByTestId, getByLabelText, getAllByLabelText } = render(
      <HeroSection {...defaultProps} isMobile={false} />,
    );

    const heroImage = getByTestId('test-hero-image');
    expect(heroImage).toBeOnTheScreen();

    const directoryImage = getByLabelText(
      ADVERTISE_IN_DIRECTORY_TEXT.DIRECTORY_IMAGE_ALT,
    );
    expect(directoryImage).toBeOnTheScreen();

    const openedDirectoryImage = getByLabelText(
      ADVERTISE_IN_DIRECTORY_TEXT.DIRECTORY_OPENED_IMAGE_ALT,
    );
    expect(openedDirectoryImage).toBeOnTheScreen();

    const allImages = getAllByLabelText(/directory image/i);
    expect(allImages).toHaveLength(2);
  });

  it('renders without testId when not provided', () => {
    const { queryByTestId } = render(<HeroSection isMobile={true} />);

    expect(queryByTestId('test-hero-image')).toBeNull();
  });

  it('applies correct accessibility properties', () => {
    const { getByLabelText } = render(<HeroSection {...defaultProps} />);

    const heroImage = getByLabelText(
      ADVERTISE_IN_DIRECTORY_TEXT.DIRECTORY_IMAGE_ALT,
    );
    expect(heroImage).toHaveProp('accessible', true);
    expect(heroImage).toHaveProp('accessibilityRole', 'image');
  });
});
