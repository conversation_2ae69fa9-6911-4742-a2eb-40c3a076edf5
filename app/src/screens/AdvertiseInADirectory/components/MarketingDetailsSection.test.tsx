import React from 'react';
import { cleanup, render } from '@testing-library/react-native';
import { MarketingDetailsSection } from './MarketingDetailsSection';
import { ADVERTISE_IN_DIRECTORY_TEXT } from '../constants';

describe('MarketingDetailsSection', () => {
  afterEach(() => {
    cleanup();
    jest.clearAllMocks();
  });

  const defaultProps = {
    isMobile: true,
  };

  it('renders all section titles correctly', () => {
    const { getByText } = render(<MarketingDetailsSection {...defaultProps} />);

    expect(
      getByText(ADVERTISE_IN_DIRECTORY_TEXT.GET_MORE_LEADS_TITLE),
    ).toBeOnTheScreen();
    expect(
      getByText(ADVERTISE_IN_DIRECTORY_TEXT.BEAT_COMPETITION_TITLE),
    ).toBeOnTheScreen();
    expect(
      getByText(ADVERTISE_IN_DIRECTORY_TEXT.SUCCESS_YEARS_TITLE),
    ).toBeOnTheScreen();
  });

  it('renders "Get more leads" section content', () => {
    const { getByText } = render(<MarketingDetailsSection {...defaultProps} />);

    expect(
      getByText(ADVERTISE_IN_DIRECTORY_TEXT.GET_MORE_LEADS_TEXT_1),
    ).toBeOnTheScreen();
    expect(
      getByText(ADVERTISE_IN_DIRECTORY_TEXT.GET_MORE_LEADS_TEXT_2),
    ).toBeOnTheScreen();
  });

  it('renders "Beat the competition" section content', () => {
    const { getByText } = render(<MarketingDetailsSection {...defaultProps} />);

    expect(
      getByText(ADVERTISE_IN_DIRECTORY_TEXT.BEAT_COMPETITION_TEXT_1),
    ).toBeOnTheScreen();
    expect(
      getByText(ADVERTISE_IN_DIRECTORY_TEXT.BEAT_COMPETITION_TEXT_2),
    ).toBeOnTheScreen();
  });

  it('renders "26 years of success" section content with bold text', () => {
    const { getByText } = render(<MarketingDetailsSection {...defaultProps} />);

    expect(
      getByText(ADVERTISE_IN_DIRECTORY_TEXT.SUCCESS_YEARS_BOLD_1),
    ).toBeOnTheScreen();
    expect(
      getByText(ADVERTISE_IN_DIRECTORY_TEXT.SUCCESS_YEARS_BOLD_2),
    ).toBeOnTheScreen();
    expect(
      getByText(ADVERTISE_IN_DIRECTORY_TEXT.SUCCESS_YEARS_BOLD_3),
    ).toBeOnTheScreen();
    expect(
      getByText(ADVERTISE_IN_DIRECTORY_TEXT.SUCCESS_YEARS_BOLD_4),
    ).toBeOnTheScreen();

    expect(
      getByText(
        ', these directories cover 358 local areas, reaching an average of 70,000 households per area.',
        { exact: false },
      ),
    ).toBeOnTheScreen();
    expect(
      getByText('They are circulated bi-monthly, with', { exact: false }),
    ).toBeOnTheScreen();
    expect(
      getByText('distributed per drop.', { exact: false }),
    ).toBeOnTheScreen();
  });

  it('renders correctly on desktop', () => {
    const { getByText } = render(
      <MarketingDetailsSection {...defaultProps} isMobile={false} />,
    );

    expect(
      getByText(ADVERTISE_IN_DIRECTORY_TEXT.GET_MORE_LEADS_TITLE),
    ).toBeOnTheScreen();
    expect(
      getByText(ADVERTISE_IN_DIRECTORY_TEXT.BEAT_COMPETITION_TITLE),
    ).toBeOnTheScreen();
    expect(
      getByText(ADVERTISE_IN_DIRECTORY_TEXT.SUCCESS_YEARS_TITLE),
    ).toBeOnTheScreen();
  });

  it('renders all three marketing sections', () => {
    const { getAllByText } = render(
      <MarketingDetailsSection {...defaultProps} />,
    );

    const titles = [
      ADVERTISE_IN_DIRECTORY_TEXT.GET_MORE_LEADS_TITLE,
      ADVERTISE_IN_DIRECTORY_TEXT.BEAT_COMPETITION_TITLE,
      ADVERTISE_IN_DIRECTORY_TEXT.SUCCESS_YEARS_TITLE,
    ];

    titles.forEach((title) => {
      expect(getAllByText(title)).toHaveLength(1);
    });
  });
});
